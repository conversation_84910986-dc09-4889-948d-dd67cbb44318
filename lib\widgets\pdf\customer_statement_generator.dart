import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/invoice.dart';
import '../../models/customer.dart';
import '../../l10n/app_localizations.dart';

// Helper classes for customer movement tracking
enum ActivityType { invoice, payment, return_ }

class CustomerActivity {
  final DateTime date;
  final ActivityType type;
  final String description;
  final double amount;
  final String invoiceId;

  CustomerActivity({
    required this.date,
    required this.type,
    required this.description,
    required this.amount,
    required this.invoiceId,
  });
}

class PaymentEntry {
  final DateTime date;
  final double amount;
  final String invoiceId;
  final double balanceAfterPayment;
  final String description;

  PaymentEntry({
    required this.date,
    required this.amount,
    required this.invoiceId,
    required this.balanceAfterPayment,
    required this.description,
  });
}

class CustomerStatementGenerator {
  static Future<void> generateAndShareStatement(
    BuildContext context,
    Customer customer,
    List<Invoice> invoices,
  ) async {
    final localizations = AppLocalizations.of(context);
    final pdf = pw.Document();

    // Load the Arabic font
    final arabicFont =
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // Load the logo image
    final logoImage = await rootBundle.load('assets/logo.jpg');
    final logoImageData = logoImage.buffer.asUint8List();
    final logo = pw.MemoryImage(logoImageData);

    // Create a theme with the Arabic font
    final theme = pw.ThemeData.withFont(
      base: ttf,
      bold: ttf,
      italic: ttf,
      boldItalic: ttf,
    );

    // Sort invoices by date (oldest first) for proper sequential numbering
    final sortedInvoices = List<Invoice>.from(invoices);
    sortedInvoices.sort((a, b) => a.date.compareTo(b.date));

    // Calculate summary data using sorted invoices
    final totalInvoices = sortedInvoices.length;
    final totalInvoiceAmount = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) =>
          sum + (invoice.totalAmount + invoice.totalReturnedAmount),
    );
    final totalPaid = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) => sum + (invoice.paidAmount ?? 0.0),
    );
    final totalReturned = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalReturnedAmount,
    );
    final currentTotalAmount = sortedInvoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final totalRemaining = currentTotalAmount - totalPaid;

    // Add page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        margin: const pw.EdgeInsets.all(20),
        maxPages: 100,
        header: (context) => _buildHeader(logo, ttf, localizations),
        footer: (context) => _buildFooter(context, ttf, localizations),
        build: (context) => [
          _buildStatementHeader(customer, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildCustomerInfo(customer, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildSummarySection(
            totalInvoices,
            totalInvoiceAmount,
            totalPaid,
            totalReturned,
            currentTotalAmount,
            totalRemaining,
            ttf,
            localizations,
          ),
          pw.SizedBox(height: 20),
          _buildPaymentMovementTracking(
            customer,
            sortedInvoices,
            paymentRecords ?? [],
            ttf,
            localizations,
          ),
          pw.SizedBox(height: 20),
          _buildCustomerMovementTracking(
            customer,
            sortedInvoices,
            paymentRecords ?? [],
            returnRecords ?? [],
            ttf,
            localizations,
          ),
        ],
      ),
    );

    // Save and share the PDF
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/customer_statement_${customer.name}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: '${localizations.customerSummary} - ${customer.name}',
    );
  }

  static pw.Widget _buildHeader(
    pw.MemoryImage logo,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: PdfColors.blue,
            width: 2,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'مؤسسة محمد علي بكري الزبيدي البيطرية',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'كشف حساب العميل',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
          pw.Container(
            width: 60,
            height: 60,
            child: pw.Image(logo),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(
    pw.Context context,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(
            color: PdfColors.blue,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تاريخ الطباعة: ${_formatDate(DateTime.now())}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            'صفحة ${context.pageNumber}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildStatementHeader(
    Customer customer,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#1976D2'),
        borderRadius: pw.BorderRadius.circular(8),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'كشف حساب العميل',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 18,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.white,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'تاريخ الإصدار: ${_formatDate(DateTime.now())}',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 12,
                  color: PdfColors.white,
                ),
              ),
            ],
          ),
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: pw.BoxDecoration(
              color: PdfColors.white,
              borderRadius: pw.BorderRadius.circular(4),
            ),
            child: pw.Text(
              'رقم العميل: ${customer.whatsappNumber}',
              style: pw.TextStyle(
                font: ttf,
                fontSize: 12,
                fontWeight: pw.FontWeight.bold,
                color: PdfColor.fromHex('#1976D2'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildCustomerInfo(
    Customer customer,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#F8F9FA'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColor.fromHex('#DEE2E6'),
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'بيانات العميل',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColor.fromHex('#495057'),
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            children: [
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'اسم العميل',
                  customer.name,
                  ttf,
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'رقم الواتساب',
                  customer.whatsappNumber,
                  ttf,
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'إجمالي المدفوع',
                  '${customer.totalPaid.toStringAsFixed(2)} ريال',
                  ttf,
                  valueColor: PdfColor.fromHex('#28A745'),
                ),
              ),
              pw.SizedBox(width: 20),
              pw.Expanded(
                flex: 2,
                child: _buildInfoItem(
                  'الرصيد المتبقي',
                  '${customer.remainingBalance.toStringAsFixed(2)} ريال',
                  ttf,
                  valueColor: customer.remainingBalance > 0
                      ? PdfColor.fromHex('#DC3545')
                      : PdfColor.fromHex('#28A745'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildInfoItem(
    String label,
    String value,
    pw.Font ttf, {
    PdfColor? valueColor,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 10,
            color: PdfColor.fromHex('#6C757D'),
          ),
        ),
        pw.SizedBox(height: 4),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: valueColor ?? PdfColor.fromHex('#212529'),
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildSummarySection(
    int totalInvoices,
    double totalInvoiceAmount,
    double totalPaid,
    double totalReturned,
    double currentTotalAmount,
    double totalRemaining,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(20),
      decoration: pw.BoxDecoration(
        gradient: pw.LinearGradient(
          colors: [
            PdfColor.fromHex('#E3F2FD'),
            PdfColor.fromHex('#F8F9FA'),
          ],
          begin: pw.Alignment.topLeft,
          end: pw.Alignment.bottomRight,
        ),
        borderRadius: pw.BorderRadius.circular(12),
        border: pw.Border.all(
          color: PdfColor.fromHex('#1976D2'),
          width: 2,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Container(
            padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
            decoration: pw.BoxDecoration(
              color: PdfColor.fromHex('#1976D2'),
              borderRadius: pw.BorderRadius.circular(6),
            ),
            child: pw.Text(
              'الملخص المالي للحساب',
              style: pw.TextStyle(
                font: ttf,
                fontSize: 16,
                fontWeight: pw.FontWeight.bold,
                color: PdfColors.white,
              ),
            ),
          ),
          pw.SizedBox(height: 16),
          // First row - Basic info
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'عدد الفواتير',
                  totalInvoices.toString(),
                  'فاتورة',
                  ttf,
                  PdfColor.fromHex('#6C757D'),
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'إجمالي قيمة الفواتير',
                  totalInvoiceAmount.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#17A2B8'),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 12),
          // Second row - Returns and net
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'إجمالي المرتجعات',
                  totalReturned.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#FFC107'),
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'صافي المبلغ المستحق',
                  currentTotalAmount.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#6F42C1'),
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 12),
          // Third row - Payments and remaining
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  'إجمالي المدفوع',
                  totalPaid.toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  PdfColor.fromHex('#28A745'),
                ),
              ),
              pw.SizedBox(width: 16),
              pw.Expanded(
                child: _buildEnhancedSummaryItem(
                  totalRemaining > 0 ? 'المبلغ المتبقي' : 'رصيد زائد للعميل',
                  totalRemaining.abs().toStringAsFixed(2),
                  'ريال سعودي',
                  ttf,
                  totalRemaining > 0
                      ? PdfColor.fromHex('#DC3545')
                      : PdfColor.fromHex('#28A745'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildEnhancedSummaryItem(
    String label,
    String value,
    String unit,
    pw.Font ttf,
    PdfColor color,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(12),
      decoration: pw.BoxDecoration(
        color: PdfColors.white,
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: color,
          width: 1.5,
        ),
        boxShadow: [
          pw.BoxShadow(
            color: PdfColor.fromHex('#00000010'),
            blurRadius: 4,
          ),
        ],
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            label,
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColor.fromHex('#6C757D'),
            ),
          ),
          pw.SizedBox(height: 6),
          pw.Row(
            crossAxisAlignment: pw.CrossAxisAlignment.end,
            children: [
              pw.Text(
                value,
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  fontWeight: pw.FontWeight.bold,
                  color: color,
                ),
              ),
              pw.SizedBox(width: 4),
              pw.Text(
                unit,
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 9,
                  color: PdfColor.fromHex('#6C757D'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildPaymentMovementTracking(
    dynamic customer,
    List<dynamic> invoices,
    List<dynamic> paymentRecords,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    // Filter payment records for this customer
    final customerPayments = paymentRecords
        .where((payment) => payment.customerId == customer.name)
        .toList();

    // Sort payments by timestamp
    customerPayments.sort((a, b) => a.timestamp.compareTo(b.timestamp));

    if (customerPayments.isEmpty) {
      return _buildEmptyPaymentTracking(ttf, localizations);
    }

    // Calculate running balance for each payment
    final paymentEntries = <PaymentEntry>[];
    double runningBalance = 0.0;

    // Calculate total invoice amount up to each payment date
    for (final payment in customerPayments) {
      // Calculate total invoices issued before or on this payment date
      final invoicesUpToDate = invoices
          .where((invoice) =>
              invoice.customer.name == customer.name &&
              invoice.date
                  .isBefore(payment.timestamp.add(const Duration(days: 1))))
          .toList();

      final totalInvoiceAmount = invoicesUpToDate.fold<double>(
        0.0,
        (sum, invoice) => sum + invoice.totalAmount,
      );

      // Calculate total payments made up to and including this payment
      final paymentsUpToDate = customerPayments
          .where((p) => p.timestamp
              .isBefore(payment.timestamp.add(const Duration(seconds: 1))))
          .toList();

      final totalPaidAmount = paymentsUpToDate.fold<double>(
        0.0,
        (sum, p) => sum + p.amount,
      );

      runningBalance = totalInvoiceAmount - totalPaidAmount;

      paymentEntries.add(PaymentEntry(
        date: payment.timestamp,
        amount: payment.amount,
        invoiceId: payment.invoiceId,
        balanceAfterPayment: runningBalance,
        description: payment.invoiceId == 'EXCESS_PAYMENT'
            ? 'دفعة زائدة'
            : 'دفعة للفاتورة ${payment.invoiceId}',
      ));
    }

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('#28A745'),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Text(
            'تتبع حركة المدفوعات والرصيد',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.white,
            ),
          ),
        ),
        pw.SizedBox(height: 16),
        _buildPaymentTable(paymentEntries, ttf),
      ],
    );
  }

  static pw.Widget _buildEmptyPaymentTracking(
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(40),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#F8F9FA'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColor.fromHex('#DEE2E6'),
          width: 1,
        ),
      ),
      child: pw.Center(
        child: pw.Text(
          'لا توجد مدفوعات للعميل',
          style: pw.TextStyle(
            font: ttf,
            fontSize: 16,
            color: PdfColor.fromHex('#6C757D'),
          ),
        ),
      ),
    );
  }

  static pw.Widget _buildPaymentTable(
    List<PaymentEntry> paymentEntries,
    pw.Font ttf,
  ) {
    return pw.Table(
      border: pw.TableBorder.all(
        color: PdfColors.grey400,
        width: 0.5,
      ),
      columnWidths: {
        0: const pw.FlexColumnWidth(1.5), // Sequential Number
        1: const pw.FlexColumnWidth(2.5), // Date
        2: const pw.FlexColumnWidth(2), // Amount
        3: const pw.FlexColumnWidth(3), // Description
        4: const pw.FlexColumnWidth(2), // Balance After Payment
      },
      children: [
        // Header row
        pw.TableRow(
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('#28A745'),
          ),
          children: [
            _buildPaymentTableCell('م', ttf, isHeader: true),
            _buildPaymentTableCell('التاريخ', ttf, isHeader: true),
            _buildPaymentTableCell('المبلغ المدفوع', ttf, isHeader: true),
            _buildPaymentTableCell('الوصف', ttf, isHeader: true),
            _buildPaymentTableCell('الرصيد بعد الدفع', ttf, isHeader: true),
          ],
        ),
        // Data rows
        ...paymentEntries.asMap().entries.map((entry) {
          final index = entry.key;
          final paymentEntry = entry.value;
          final sequentialNumber = index + 1;

          return pw.TableRow(
            decoration: pw.BoxDecoration(
              color: index % 2 == 0
                  ? PdfColors.white
                  : PdfColor.fromHex('#F9F9F9'),
            ),
            children: [
              _buildPaymentTableCell(sequentialNumber.toString(), ttf),
              _buildPaymentTableCell(_formatDateShort(paymentEntry.date), ttf),
              _buildPaymentTableCell(
                '${paymentEntry.amount.toStringAsFixed(2)} ريال',
                ttf,
                color: PdfColor.fromHex('#28A745'),
              ),
              _buildPaymentTableCell(paymentEntry.description, ttf),
              _buildPaymentTableCell(
                '${paymentEntry.balanceAfterPayment.toStringAsFixed(2)} ريال',
                ttf,
                color: paymentEntry.balanceAfterPayment > 0
                    ? PdfColor.fromHex('#DC3545')
                    : PdfColor.fromHex('#28A745'),
              ),
            ],
          );
        }).toList(),
      ],
    );
  }

  static pw.Widget _buildPaymentTableCell(
    String text,
    pw.Font ttf, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.white : (color ?? PdfColors.grey800),
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static pw.Widget _buildCustomerMovementTracking(
    dynamic customer,
    List<dynamic> invoices,
    List<dynamic> paymentRecords,
    List<dynamic> returnRecords,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    // Create a comprehensive timeline of customer activities
    final activities = <CustomerActivity>[];

    // Add invoice activities
    for (final invoice in invoices) {
      activities.add(CustomerActivity(
        date: invoice.date,
        type: ActivityType.invoice,
        description: 'فاتورة رقم ${invoice.id}',
        amount: invoice.totalAmount,
        invoiceId: invoice.id,
      ));
    }

    // Add payment activities
    for (final payment in paymentRecords) {
      if (payment.customerId == customer.name) {
        activities.add(CustomerActivity(
          date: payment.timestamp,
          type: ActivityType.payment,
          description: payment.invoiceId == 'EXCESS_PAYMENT'
              ? 'دفعة زائدة'
              : 'دفعة للفاتورة ${payment.invoiceId}',
          amount: payment.amount,
          invoiceId: payment.invoiceId,
        ));
      }
    }

    // Add return activities
    for (final returnRecord in returnRecords) {
      final relatedInvoice = invoices.firstWhere(
        (inv) => inv.id == returnRecord.invoiceId,
        orElse: () => null,
      );
      if (relatedInvoice?.customer?.name == customer.name) {
        activities.add(CustomerActivity(
          date: returnRecord.returnDate,
          type: ActivityType.return_,
          description: 'مرتجع من الفاتورة ${returnRecord.invoiceId}',
          amount: returnRecord.totalReturnAmount,
          invoiceId: returnRecord.invoiceId,
        ));
      }
    }

    // Sort activities by date
    activities.sort((a, b) => a.date.compareTo(b.date));

    // Generate date range for the tracking calendar
    if (activities.isEmpty) {
      return _buildEmptyMovementTracking(ttf, localizations);
    }

    final startDate = activities.first.date;
    final endDate = activities.last.date;
    final dateRange = _generateDateRange(startDate, endDate);

    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Container(
          padding: const pw.EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: pw.BoxDecoration(
            color: PdfColor.fromHex('#1976D2'),
            borderRadius: pw.BorderRadius.circular(6),
          ),
          child: pw.Text(
            'تتبع حركة العميل',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.white,
            ),
          ),
        ),
        pw.SizedBox(height: 16),
        _buildMovementCalendar(dateRange, activities, ttf),
      ],
    );
  }

  static String _formatDate(DateTime date) {
    const monthNames = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];

    final day = date.day.toString().padLeft(2, '0');
    final month = monthNames[date.month - 1];
    final year = date.year.toString();

    return '$day $month $year';
  }

  static String _formatDateShort(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  static pw.Widget _buildEmptyMovementTracking(
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(40),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#F8F9FA'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColor.fromHex('#DEE2E6'),
          width: 1,
        ),
      ),
      child: pw.Center(
        child: pw.Text(
          'لا توجد حركات للعميل',
          style: pw.TextStyle(
            font: ttf,
            fontSize: 16,
            color: PdfColor.fromHex('#6C757D'),
          ),
        ),
      ),
    );
  }

  static List<DateTime> _generateDateRange(
      DateTime startDate, DateTime endDate) {
    final dates = <DateTime>[];
    var currentDate = DateTime(startDate.year, startDate.month, startDate.day);
    final end = DateTime(endDate.year, endDate.month, endDate.day);

    while (currentDate.isBefore(end) || currentDate.isAtSameMomentAs(end)) {
      dates.add(currentDate);
      currentDate = currentDate.add(const Duration(days: 1));
    }

    return dates;
  }

  static pw.Widget _buildMovementCalendar(
    List<DateTime> dateRange,
    List<CustomerActivity> activities,
    pw.Font ttf,
  ) {
    // Group activities by date
    final activitiesByDate = <DateTime, List<CustomerActivity>>{};
    for (final activity in activities) {
      final dateKey =
          DateTime(activity.date.year, activity.date.month, activity.date.day);
      activitiesByDate.putIfAbsent(dateKey, () => []).add(activity);
    }

    // Create calendar grid
    final calendarRows = <pw.TableRow>[];

    // Add header
    calendarRows.add(
      pw.TableRow(
        decoration: pw.BoxDecoration(
          color: PdfColor.fromHex('#1976D2'),
        ),
        children: [
          _buildCalendarCell('التاريخ', ttf, isHeader: true),
          _buildCalendarCell('النشاط', ttf, isHeader: true),
          _buildCalendarCell('المبلغ', ttf, isHeader: true),
          _buildCalendarCell('الوصف', ttf, isHeader: true),
        ],
      ),
    );

    // Add date rows
    for (int i = 0; i < dateRange.length; i++) {
      final date = dateRange[i];
      final dayActivities = activitiesByDate[date] ?? [];

      if (dayActivities.isEmpty) {
        // Empty day
        calendarRows.add(
          pw.TableRow(
            decoration: pw.BoxDecoration(
              color: i % 2 == 0 ? PdfColors.white : PdfColor.fromHex('#F9F9F9'),
            ),
            children: [
              _buildCalendarCell(_formatDateShort(date), ttf),
              _buildCalendarCell('لا يوجد نشاط', ttf,
                  color: PdfColor.fromHex('#6C757D')),
              _buildCalendarCell('-', ttf),
              _buildCalendarCell('-', ttf),
            ],
          ),
        );
      } else {
        // Day with activities
        for (int j = 0; j < dayActivities.length; j++) {
          final activity = dayActivities[j];
          calendarRows.add(
            pw.TableRow(
              decoration: pw.BoxDecoration(
                color:
                    i % 2 == 0 ? PdfColors.white : PdfColor.fromHex('#F9F9F9'),
              ),
              children: [
                _buildCalendarCell(
                  j == 0 ? _formatDateShort(date) : '',
                  ttf,
                ),
                _buildCalendarCell(
                  _getActivityTypeText(activity.type),
                  ttf,
                  color: _getActivityColor(activity.type),
                ),
                _buildCalendarCell(
                  '${activity.amount.toStringAsFixed(2)} ريال',
                  ttf,
                  color: _getActivityColor(activity.type),
                ),
                _buildCalendarCell(
                  activity.description,
                  ttf,
                ),
              ],
            ),
          );
        }
      }
    }

    return pw.Table(
      border: pw.TableBorder.all(
        color: PdfColors.grey400,
        width: 0.5,
      ),
      columnWidths: {
        0: const pw.FlexColumnWidth(2), // Date
        1: const pw.FlexColumnWidth(2), // Activity Type
        2: const pw.FlexColumnWidth(2), // Amount
        3: const pw.FlexColumnWidth(4), // Description
      },
      children: calendarRows,
    );
  }

  static pw.Widget _buildCalendarCell(
    String text,
    pw.Font ttf, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.white : (color ?? PdfColors.grey800),
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static String _getActivityTypeText(ActivityType type) {
    switch (type) {
      case ActivityType.invoice:
        return 'فاتورة';
      case ActivityType.payment:
        return 'دفعة';
      case ActivityType.return_:
        return 'مرتجع';
    }
  }

  static PdfColor _getActivityColor(ActivityType type) {
    switch (type) {
      case ActivityType.invoice:
        return PdfColor.fromHex('#17A2B8');
      case ActivityType.payment:
        return PdfColor.fromHex('#28A745');
      case ActivityType.return_:
        return PdfColor.fromHex('#FFC107');
    }
  }
}
