import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'models/product.dart';
import 'models/customer.dart';
import 'models/invoice.dart';
import 'models/supplier.dart';
import 'models/supplier_invoice.dart';
import 'models/consumable.dart';
import 'models/return.dart';
import 'models/consumable_collection.dart';
import 'models/payment_record.dart';
import 'cubits/products_cubit.dart';
import 'cubits/customers_cubit.dart';
import 'cubits/invoices_cubit.dart';
import 'cubits/suppliers_cubit.dart';
import 'cubits/supplier_invoices_cubit.dart';
import 'cubits/cashbox_cubit.dart';
import 'cubits/consumables_cubit.dart';
import 'cubits/returns_cubit.dart';
import 'cubits/consumable_collections_cubit.dart';
import 'cubits/payment_records_cubit.dart';
import 'screens/main_screen.dart';
import 'l10n/app_localizations.dart';
import 'constants/app_theme.dart';

class MyApp extends StatelessWidget {
  final Box<Product> productsBox;
  final Box<Customer> customersBox;
  final Box<Invoice> invoicesBox;
  final Box<Supplier> suppliersBox;
  final Box<SupplierInvoice> supplierInvoicesBox;
  final Box<Consumable> consumablesBox;
  final Box<Return> returnsBox;
  final Box<ConsumableCollection> collectionsBox;
  final Box<PaymentRecord> paymentRecordsBox;

  const MyApp({
    super.key,
    required this.productsBox,
    required this.customersBox,
    required this.invoicesBox,
    required this.suppliersBox,
    required this.supplierInvoicesBox,
    required this.consumablesBox,
    required this.returnsBox,
    required this.collectionsBox,
    required this.paymentRecordsBox,
  });

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(create: (context) => ProductsCubit(productsBox)),
        BlocProvider(create: (context) => CustomersCubit(customersBox)),
        BlocProvider(
            create: (context) => InvoicesCubit(invoicesBox, productsBox)),
        BlocProvider(create: (context) => SuppliersCubit(suppliersBox)),
        BlocProvider(
            create: (context) => SupplierInvoicesCubit(supplierInvoicesBox)),
        BlocProvider(
            create: (context) =>
                CashboxCubit(invoicesBox, productsBox, consumablesBox)),
        BlocProvider(create: (context) => ConsumablesCubit(consumablesBox)),
        BlocProvider(create: (context) => ReturnsCubit(returnsBox)),
        BlocProvider(
            create: (context) => ConsumableCollectionsCubit(collectionsBox)),
        BlocProvider(
            create: (context) => PaymentRecordsCubit(paymentRecordsBox)),
      ],
      child: Builder(builder: (context) {
        // Connect cubits for real-time updates
        final invoicesCubit = context.read<InvoicesCubit>();
        final cashboxCubit = context.read<CashboxCubit>();
        final productsCubit = context.read<ProductsCubit>();
        final customersCubit = context.read<CustomersCubit>();
        final suppliersCubit = context.read<SuppliersCubit>();
        final supplierInvoicesCubit = context.read<SupplierInvoicesCubit>();
        final returnsCubit = context.read<ReturnsCubit>();
        final paymentRecordsCubit = context.read<PaymentRecordsCubit>();

        // Set up connections between cubits
        invoicesCubit.setCashboxCubit(cashboxCubit);
        invoicesCubit.setProductsCubit(productsCubit);
        invoicesCubit.setCustomersCubit(customersCubit);
        invoicesCubit.setReturnsCubit(returnsCubit);
        invoicesCubit.setPaymentRecordsCubit(paymentRecordsCubit);
        productsCubit.setCashboxCubit(cashboxCubit);

        // Connect customers cubit to invoices box for total paid calculation
        customersCubit.setInvoicesBox(invoicesBox);

        // Connect customers cubit to invoices cubit for deleting invoices
        customersCubit.setInvoicesCubit(invoicesCubit);

        // Connect suppliers cubit to supplier invoices box for total paid calculation
        suppliersCubit.setSupplierInvoicesBox(supplierInvoicesBox);
        suppliersCubit.setSupplierInvoicesCubit(supplierInvoicesCubit);

        // Connect supplier invoices cubit to other cubits
        supplierInvoicesCubit.setSuppliersCubit(suppliersCubit);
        supplierInvoicesCubit.setProductsCubit(productsCubit);
        supplierInvoicesCubit.setCashboxCubit(cashboxCubit);

        // Initialize customer total paid calculations
        customersCubit.updateCustomerTotalPaid();

        // Initialize supplier total paid calculations
        suppliersCubit.updateSupplierTotalPaid();

        return MaterialApp(
          debugShowCheckedModeBanner: false,
          title: 'مؤسسة الزبيدي', // Default title in Arabic
          theme: AppTheme.lightTheme.copyWith(
            textTheme:
                GoogleFonts.poppinsTextTheme(AppTheme.lightTheme.textTheme),
          ),
          locale: const Locale('ar'), // Set Arabic as default language
          supportedLocales: AppLocalizations.supportedLocales,
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          home: const MainScreen(),
          builder: (context, child) {
            // This ensures RTL for Arabic
            return Directionality(
              textDirection: TextDirection.rtl,
              child: child!,
            );
          },
        );
      }),
    );
  }
}
