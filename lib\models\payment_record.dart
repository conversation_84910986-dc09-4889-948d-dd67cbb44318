import 'package:hive/hive.dart';

part 'payment_record.g.dart';

@HiveType(typeId: 20)
class PaymentRecord extends HiveObject {
  @HiveField(0)
  String id;

  @HiveField(1)
  String customerId;

  @HiveField(2)
  String invoiceId;

  @HiveField(3)
  double amount;

  @HiveField(4)
  DateTime timestamp;

  PaymentRecord({
    required this.id,
    required this.customerId,
    required this.invoiceId,
    required this.amount,
    required this.timestamp,
  });
}
