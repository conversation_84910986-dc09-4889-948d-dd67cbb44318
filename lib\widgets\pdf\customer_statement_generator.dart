import 'package:flutter/services.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:share_plus/share_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'package:flutter/material.dart';
import '../../models/invoice.dart';
import '../../models/customer.dart';
import '../../l10n/app_localizations.dart';
import '../../constants/app_colors.dart';

class CustomerStatementGenerator {
  static Future<void> generateAndShareStatement(
    BuildContext context,
    Customer customer,
    List<Invoice> invoices,
  ) async {
    final localizations = AppLocalizations.of(context);
    final pdf = pw.Document();

    // Load the Arabic font
    final arabicFont =
        await rootBundle.load('assets/fonts/NotoSansArabic-Regular.ttf');
    final ttf = pw.Font.ttf(arabicFont);

    // Load the logo image
    final logoImage = await rootBundle.load('assets/logo.jpg');
    final logoImageData = logoImage.buffer.asUint8List();
    final logo = pw.MemoryImage(logoImageData);

    // Create a theme with the Arabic font
    final theme = pw.ThemeData.withFont(
      base: ttf,
      bold: ttf,
      italic: ttf,
      boldItalic: ttf,
    );

    // Calculate summary data
    final totalInvoices = invoices.length;
    final totalInvoiceAmount = invoices.fold<double>(
      0.0,
      (sum, invoice) =>
          sum + (invoice.totalAmount + invoice.totalReturnedAmount),
    );
    final totalPaid = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + (invoice.paidAmount ?? 0.0),
    );
    final totalReturned = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalReturnedAmount,
    );
    final currentTotalAmount = invoices.fold<double>(
      0.0,
      (sum, invoice) => sum + invoice.totalAmount,
    );
    final totalRemaining = currentTotalAmount - totalPaid;

    // Add page
    pdf.addPage(
      pw.MultiPage(
        pageFormat: PdfPageFormat.a4,
        textDirection: pw.TextDirection.rtl,
        theme: theme,
        margin: const pw.EdgeInsets.all(20),
        maxPages: 100,
        header: (context) => _buildHeader(logo, ttf, localizations),
        footer: (context) => _buildFooter(context, ttf, localizations),
        build: (context) => [
          _buildCustomerInfo(customer, ttf, localizations),
          pw.SizedBox(height: 20),
          _buildSummarySection(
            totalInvoices,
            totalInvoiceAmount,
            totalPaid,
            totalReturned,
            currentTotalAmount,
            totalRemaining,
            ttf,
            localizations,
          ),
          pw.SizedBox(height: 20),
          _buildInvoicesTable(invoices, ttf, localizations),
        ],
      ),
    );

    // Save and share the PDF
    final output = await getTemporaryDirectory();
    final file = File('${output.path}/customer_statement_${customer.name}.pdf');
    await file.writeAsBytes(await pdf.save());

    await Share.shareXFiles(
      [XFile(file.path)],
      text: '${localizations.customerSummary} - ${customer.name}',
    );
  }

  static pw.Widget _buildHeader(
    pw.MemoryImage logo,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(bottom: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: PdfColors.blue,
            width: 2,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Text(
                'مؤسسة محمد علي بكري الزبيدي البيطرية',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 16,
                  fontWeight: pw.FontWeight.bold,
                  color: PdfColors.blue,
                ),
              ),
              pw.SizedBox(height: 4),
              pw.Text(
                'كشف حساب العميل',
                style: pw.TextStyle(
                  font: ttf,
                  fontSize: 14,
                  color: PdfColors.grey700,
                ),
              ),
            ],
          ),
          pw.Container(
            width: 60,
            height: 60,
            child: pw.Image(logo),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildFooter(
    pw.Context context,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 20),
      decoration: const pw.BoxDecoration(
        border: pw.Border(
          top: pw.BorderSide(
            color: PdfColors.blue,
            width: 1,
          ),
        ),
      ),
      child: pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
        children: [
          pw.Text(
            'تاريخ الطباعة: ${_formatDate(DateTime.now())}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
          pw.Text(
            'صفحة ${context.pageNumber}',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 10,
              color: PdfColors.grey600,
            ),
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildCustomerInfo(
    Customer customer,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#E3F2FD'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColors.blue,
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'بيانات العميل',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            children: [
              pw.Expanded(
                child: pw.Text(
                  'اسم العميل: ${customer.name}',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 12,
                    color: PdfColors.grey800,
                  ),
                ),
              ),
              pw.Expanded(
                child: pw.Text(
                  'رقم الواتساب: ${customer.whatsappNumber}',
                  style: pw.TextStyle(
                    font: ttf,
                    fontSize: 12,
                    color: PdfColors.grey800,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSummarySection(
    int totalInvoices,
    double totalInvoiceAmount,
    double totalPaid,
    double totalReturned,
    double currentTotalAmount,
    double totalRemaining,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(16),
      decoration: pw.BoxDecoration(
        color: PdfColor.fromHex('#F5F5F5'),
        borderRadius: pw.BorderRadius.circular(8),
        border: pw.Border.all(
          color: PdfColors.grey400,
          width: 1,
        ),
      ),
      child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text(
            'الملخص المالي',
            style: pw.TextStyle(
              font: ttf,
              fontSize: 16,
              fontWeight: pw.FontWeight.bold,
              color: PdfColors.blue,
            ),
          ),
          pw.SizedBox(height: 12),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildSummaryItem(
                  'إجمالي الفواتير',
                  totalInvoices.toString(),
                  ttf,
                ),
              ),
              pw.Expanded(
                child: _buildSummaryItem(
                  'إجمالي قيمة الفواتير',
                  '${totalInvoiceAmount.toStringAsFixed(2)} ريال',
                  ttf,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildSummaryItem(
                  'إجمالي المرتجعات',
                  '${totalReturned.toStringAsFixed(2)} ريال',
                  ttf,
                  color: PdfColor.fromHex('#FF9800'),
                ),
              ),
              pw.Expanded(
                child: _buildSummaryItem(
                  'صافي المبلغ',
                  '${currentTotalAmount.toStringAsFixed(2)} ريال',
                  ttf,
                ),
              ),
            ],
          ),
          pw.SizedBox(height: 8),
          pw.Row(
            children: [
              pw.Expanded(
                child: _buildSummaryItem(
                  'إجمالي المدفوع',
                  '${totalPaid.toStringAsFixed(2)} ريال',
                  ttf,
                  color: PdfColor.fromHex('#4CAF50'),
                ),
              ),
              pw.Expanded(
                child: _buildSummaryItem(
                  'المبلغ المتبقي',
                  '${totalRemaining.toStringAsFixed(2)} ريال',
                  ttf,
                  color: totalRemaining > 0
                      ? PdfColor.fromHex('#F44336')
                      : PdfColor.fromHex('#4CAF50'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  static pw.Widget _buildSummaryItem(
    String label,
    String value,
    pw.Font ttf, {
    PdfColor? color,
  }) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          label,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 10,
            color: PdfColors.grey600,
          ),
        ),
        pw.SizedBox(height: 2),
        pw.Text(
          value,
          style: pw.TextStyle(
            font: ttf,
            fontSize: 12,
            fontWeight: pw.FontWeight.bold,
            color: color ?? PdfColors.grey800,
          ),
        ),
      ],
    );
  }

  static pw.Widget _buildInvoicesTable(
    List<Invoice> invoices,
    pw.Font ttf,
    AppLocalizations localizations,
  ) {
    return pw.Column(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Text(
          'تفاصيل الفواتير',
          style: pw.TextStyle(
            font: ttf,
            fontSize: 16,
            fontWeight: pw.FontWeight.bold,
            color: PdfColors.blue,
          ),
        ),
        pw.SizedBox(height: 12),
        pw.Table(
          border: pw.TableBorder.all(
            color: PdfColors.grey400,
            width: 0.5,
          ),
          columnWidths: {
            0: const pw.FlexColumnWidth(2), // Invoice ID
            1: const pw.FlexColumnWidth(2), // Date
            2: const pw.FlexColumnWidth(2), // Original Amount
            3: const pw.FlexColumnWidth(2), // Returned
            4: const pw.FlexColumnWidth(2), // Net Amount
            5: const pw.FlexColumnWidth(2), // Paid
            6: const pw.FlexColumnWidth(2), // Remaining
          },
          children: [
            // Header row
            pw.TableRow(
              decoration: pw.BoxDecoration(
                color: PdfColors.blue,
              ),
              children: [
                _buildTableCell('رقم الفاتورة', ttf, isHeader: true),
                _buildTableCell('التاريخ', ttf, isHeader: true),
                _buildTableCell('المبلغ الأصلي', ttf, isHeader: true),
                _buildTableCell('المرتجع', ttf, isHeader: true),
                _buildTableCell('صافي المبلغ', ttf, isHeader: true),
                _buildTableCell('المدفوع', ttf, isHeader: true),
                _buildTableCell('المتبقي', ttf, isHeader: true),
              ],
            ),
            // Data rows
            ...invoices.map((invoice) {
              final originalAmount =
                  invoice.totalAmount + invoice.totalReturnedAmount;
              final paidAmount = invoice.paidAmount ?? 0.0;
              final remainingAmount = invoice.totalAmount - paidAmount;

              return pw.TableRow(
                decoration: pw.BoxDecoration(
                  color: invoices.indexOf(invoice) % 2 == 0
                      ? PdfColors.white
                      : PdfColor.fromHex('#F9F9F9'),
                ),
                children: [
                  _buildTableCell(invoice.id, ttf),
                  _buildTableCell(_formatDate(invoice.date), ttf),
                  _buildTableCell(
                    '${originalAmount.toStringAsFixed(2)} ريال',
                    ttf,
                  ),
                  _buildTableCell(
                    '${invoice.totalReturnedAmount.toStringAsFixed(2)} ريال',
                    ttf,
                    color: invoice.totalReturnedAmount > 0
                        ? PdfColor.fromHex('#FF9800')
                        : null,
                  ),
                  _buildTableCell(
                    '${invoice.totalAmount.toStringAsFixed(2)} ريال',
                    ttf,
                  ),
                  _buildTableCell(
                    '${paidAmount.toStringAsFixed(2)} ريال',
                    ttf,
                    color: paidAmount > 0 ? PdfColor.fromHex('#4CAF50') : null,
                  ),
                  _buildTableCell(
                    '${remainingAmount.toStringAsFixed(2)} ريال',
                    ttf,
                    color: remainingAmount > 0
                        ? PdfColor.fromHex('#F44336')
                        : PdfColor.fromHex('#4CAF50'),
                  ),
                ],
              );
            }).toList(),
          ],
        ),
      ],
    );
  }

  static pw.Widget _buildTableCell(
    String text,
    pw.Font ttf, {
    bool isHeader = false,
    PdfColor? color,
  }) {
    return pw.Container(
      padding: const pw.EdgeInsets.all(8),
      child: pw.Text(
        text,
        style: pw.TextStyle(
          font: ttf,
          fontSize: isHeader ? 10 : 9,
          fontWeight: isHeader ? pw.FontWeight.bold : pw.FontWeight.normal,
          color: isHeader ? PdfColors.white : (color ?? PdfColors.grey800),
        ),
        textAlign: pw.TextAlign.center,
      ),
    );
  }

  static String _formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }
}
